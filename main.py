#!/usr/bin/env python3
"""
Main script for running different investment strategies.

Usage:
    uv run main.py WeeklyInvest
    uv run main.py DrawdownInvest
    uv run main.py --help
"""

import sys
import argparse
from engine.backtest import BacktestEngine, PerformanceAnalyzer
from strategies.weekly_invest import WeeklyInvestStrategy
from strategies.drawdown_invest import DrawdownInvestStrategy
from config.config_manager import get_config_manager, get_config

def get_strategy(strategy_name, **kwargs):
    """Factory function to create strategy instances"""
    strategies = {
        'WeeklyInvest': WeeklyInvestStrategy,
        'DrawdownInvest': DrawdownInvestStrategy,
    }
    
    if strategy_name not in strategies:
        available = ', '.join(strategies.keys())
        raise ValueError(f"Unknown strategy '{strategy_name}'. Available strategies: {available}")
    
    return strategies[strategy_name](**kwargs)

def run_backtest(strategy_name, config=None, **strategy_kwargs):
    """Run a backtest with the specified strategy"""
    if config is None:
        config = DEFAULT_CONFIG.copy()
    
    # Create strategy instance
    strategy = get_strategy(strategy_name, **strategy_kwargs)
    
    print(f"🚀 Running backtest with strategy: {strategy.get_name()}")
    print(f"📝 Description: {strategy.get_description()}")
    print(f"📅 Period: {config['start_date']} to {config['end_date']}")
    print(f"💰 Assets: {list(config['assets'].keys())}")
    print("=" * 60)
    
    # Create and run backtest engine
    engine = BacktestEngine(config, strategy)
    portfolio = engine.run_simulation()
    
    # Analyze performance
    analyzer = PerformanceAnalyzer(portfolio)
    metrics = analyzer.calculate_metrics()
    
    # Generate outputs
    analyzer.generate_plots()
    analyzer.save_trade_log()
    analyzer.plot_per_ticker_drawdown()
    analyzer.plot_equity_forecast()
    
    # Print results
    print("\n📊 Backtest completed successfully!")
    print("📈 Performance Metrics:")
    for k, v in metrics.items():
        if isinstance(v, float):
            print(f"   {k}: {v:.4f}")
        else:
            print(f"   {k}: {v}")
    
    # Print trade summary
    total_trades = len(portfolio.trade_log)
    buy_trades = len([t for t in portfolio.trade_log if t['type'] == 'BUY'])
    dividend_trades = len([t for t in portfolio.trade_log if t['type'] == 'DIVIDEND'])
    
    print(f"\n💼 Trade Summary:")
    print(f"   Total trades: {total_trades}")
    print(f"   Buy trades: {buy_trades}")
    print(f"   Dividend reinvestments: {dividend_trades}")
    
    final_value = portfolio.portfolio_value()
    total_invested = sum([t['price'] * t['shares'] for t in portfolio.trade_log if t['type'] == 'BUY'])
    roi = ((final_value - total_invested) / total_invested * 100) if total_invested > 0 else 0
    
    print(f"\n💵 Financial Summary:")
    print(f"   Total invested: ${total_invested:,.2f}")
    print(f"   Final portfolio value: ${final_value:,.2f}")
    print(f"   Total return: ${final_value - total_invested:,.2f}")
    print(f"   ROI: {roi:.2f}%")
    
    print(f"\n📁 Output files generated:")
    print(f"   - equity_curve.png")
    print(f"   - drawdown.png")
    print(f"   - per_ticker_drawdown.png")
    print(f"   - equity_forecast.png")
    print(f"   - trade_log.csv")

def main():
    parser = argparse.ArgumentParser(
        description="Run investment strategy backtests",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Available Strategies:
  WeeklyInvest     - Invest a fixed amount every week
  DrawdownInvest   - Invest when assets drop by a threshold

Examples:
  uv run main.py WeeklyInvest
  uv run main.py DrawdownInvest
  uv run main.py WeeklyInvest --weekly-amount 200
  uv run main.py DrawdownInvest --drop-threshold 0.10
        """
    )
    
    parser.add_argument('strategy', help='Strategy to run (WeeklyInvest, DrawdownInvest)')
    
    # Strategy-specific arguments
    parser.add_argument('--weekly-amount', type=float, default=100,
                       help='Weekly investment amount for WeeklyInvest strategy (default: 100)')
    parser.add_argument('--investment-day', default='monday',
                       help='Day of week to invest for WeeklyInvest (default: monday)')
    parser.add_argument('--drop-threshold', type=float, default=0.05,
                       help='Drop threshold for DrawdownInvest strategy (default: 0.05)')
    parser.add_argument('--investment-amount', type=float, default=100,
                       help='Investment amount for DrawdownInvest strategy (default: 100)')
    
    # General arguments
    parser.add_argument('--start-date', default='2002-11-18',
                       help='Start date for backtest (default: 2002-11-18)')
    parser.add_argument('--end-date', default='2025-05-05',
                       help='End date for backtest (default: 2025-05-05)')
    
    args = parser.parse_args()
    
    # Prepare config
    config = DEFAULT_CONFIG.copy()
    config['start_date'] = args.start_date
    config['end_date'] = args.end_date
    
    # Prepare strategy kwargs
    strategy_kwargs = {}
    if args.strategy == 'WeeklyInvest':
        strategy_kwargs = {
            'weekly_amount': args.weekly_amount,
            'investment_day': args.investment_day
        }
    elif args.strategy == 'DrawdownInvest':
        strategy_kwargs = {
            'drop_threshold': args.drop_threshold,
            'investment_amount': args.investment_amount
        }
    
    try:
        run_backtest(args.strategy, config, **strategy_kwargs)
    except ValueError as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  Backtest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
