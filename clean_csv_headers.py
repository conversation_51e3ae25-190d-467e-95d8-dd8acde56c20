import pandas as pd
import os

def clean_csv_file(filepath):
    """
    Clean CSV file by removing the 3-row header and creating a proper single header.
    Reorders columns to have Date first.
    """
    try:
        # Read the CSV skipping the 3-row header
        data = pd.read_csv(filepath, skiprows=3, 
                          names=['Date', 'Close', 'High', 'Low', 'Open', 'Volume'])
        
        # Reorder columns to have Date first, then the OHLCV data
        data = data[['Date', 'Close', 'High', 'Low', 'Open', 'Volume']]
        
        # Save the cleaned file
        data.to_csv(filepath, index=False)
        print(f"Successfully cleaned {filepath}")
        
    except Exception as e:
        print(f"Error cleaning {filepath}: {e}")

def clean_all_csv_files():
    """
    Clean all CSV files in the data_cache directory.
    """
    data_cache_dir = "data_cache"
    
    if not os.path.exists(data_cache_dir):
        print(f"Directory {data_cache_dir} does not exist")
        return
    
    csv_files = [f for f in os.listdir(data_cache_dir) if f.endswith('.csv')]
    
    if not csv_files:
        print("No CSV files found in data_cache directory")
        return
    
    print(f"Found {len(csv_files)} CSV files to clean:")
    for csv_file in csv_files:
        print(f"  - {csv_file}")
    
    for csv_file in csv_files:
        filepath = os.path.join(data_cache_dir, csv_file)
        clean_csv_file(filepath)

if __name__ == "__main__":
    # Clean all CSV files
    clean_all_csv_files()
