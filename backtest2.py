import yfinance as yf
import pandas as pd
import matplotlib.pyplot as plt
from pandas_market_calendars import get_calendar
from datetime import datetime
import numpy as np
from scipy.stats import norm
import os
from utils.data_loader import load_data
from utils.data_downloader import download_and_save_data

class DataFetcher:
    def __init__(self):
        self.cal = get_calendar('NYSE')
    
    def fetch_ohlcv(self, tickers, start_date, end_date):
        schedule = self.cal.schedule(start_date, end_date)
        valid_dates = schedule.index
        all_data = {}
        for ticker in tickers:
            filename = f"data_{ticker}_{start_date}_{end_date}.csv"
            filepath = os.path.join("data_cache", filename)

            # Try to load existing data using our clean data loader
            data = load_data(filepath)

            if data is None or data.empty:
                # Download and save with clean format
                data = download_and_save_data(ticker, start_date, end_date, filepath)
                if data is not None:
                    # Convert to the format expected by the rest of the code
                    data = data.set_index('Date')

            all_data[ticker] = data
        combined = pd.concat(all_data, axis=1)
        aligned_data = combined.reindex(valid_dates).ffill()
        return aligned_data

class Portfolio:
    def __init__(self, initial_capital, assets):
        self.cash = initial_capital
        self.holdings = {ticker: 0 for ticker in assets}
        self.allocations = assets
        self.value_history = []
        self.trade_log = []
        self.holdings_history = []

    def execute_trade(self, date, ticker, price, invest_amount):
        shares = invest_amount / price
        if shares > 0:
            self.holdings[ticker] += shares
            self.cash -= invest_amount
            self.trade_log.append({
                'date': date,
                'ticker': ticker,
                'shares': shares,
                'price': price,
                'type': 'BUY'
            })

    def reinvest_dividends(self, date, ticker, dividend, price):
        if dividend > 0 and self.holdings[ticker] > 0:
            dividend_amount = self.holdings[ticker] * dividend
            shares = dividend_amount / price
            self.holdings[ticker] += shares
            self.trade_log.append({
                'date': date,
                'ticker': ticker,
                'shares': shares,
                'price': price,
                'type': 'DIVIDEND'
            })

    def portfolio_value(self):
        if not hasattr(self, 'current_prices'):
            return self.cash
        holdings_value = sum(
            self.holdings[ticker] * self.current_prices[ticker]['Close']
            for ticker in self.holdings
        )
        return holdings_value + self.cash

class BacktestEngine:
    def __init__(self, config):
        self.config = config
        self.data_fetcher = DataFetcher()
        self.portfolio = Portfolio(
            config['initial_capital'], 
            config['assets']
        )
        self.last_peaks = {ticker: None for ticker in config['assets']}
        self.last_invest_dates = {ticker: None for ticker in config['assets']}
        self.invested_sum = 0

    def run_simulation(self):
        data = self.data_fetcher.fetch_ohlcv(
            list(self.config['assets'].keys()),
            self.config['start_date'],
            self.config['end_date']
        )
        start_date = pd.to_datetime(self.config['start_date'])
        investments_made = 0
        for ticker in self.config['assets']:
            try:
                first_valid = data[ticker]['Close'].first_valid_index()
                if first_valid is not None and pd.to_datetime(first_valid) > start_date:
                    print(f"Warning: {ticker} has no data at start date {start_date.date()}. First available: {first_valid.date()}")
                elif first_valid is None:
                    print(f"Warning: {ticker} has no valid data in the selected period.")
            except Exception as e:
                print(f"Warning: Could not check data for {ticker}: {e}")
        
        for date, prices in data.iterrows():
            self.portfolio.current_prices = {
                t: {'Close': prices[t]['Close']} for t in self.config['assets']
            }
            # Dividend reinvestment
            for ticker in self.config['assets']:
                if 'Dividends' in prices[ticker]:
                    self.portfolio.reinvest_dividends(
                        date, ticker,
                        prices[ticker]['Dividends'],
                        prices[ticker]['Close']
                    )
            # Drop-based investment logic
            for ticker in self.config['assets']:
                price = prices[ticker]['Close']
                if self.last_peaks[ticker] is None or price > self.last_peaks[ticker]:
                    self.last_peaks[ticker] = price
                drop = (self.last_peaks[ticker] - price) / self.last_peaks[ticker] if self.last_peaks[ticker] else 0
                if drop >= self.config.get('drop_threshold', 0.05):
                    # Invest only if not already invested on this drop
                    if self.last_invest_dates[ticker] != date:
                        invest_amount = self.config.get('investment_amount', 100)
                        self.portfolio.cash += invest_amount
                        self.invested_sum += invest_amount
                        self.portfolio.execute_trade(date, ticker, price, invest_amount)
                        self.last_invest_dates[ticker] = date
                        investments_made += 1
                        print(f"Invested {invest_amount} in {ticker} on {date.date()} at price {price:.2f} (drop {drop*100:.2f}%)")
            # Record daily portfolio value
            self.portfolio.value_history.append({
                'date': date,
                'value': self.portfolio.portfolio_value()
            })
            self.portfolio.holdings_history.append({
                'date': date,
                'holdings': self.portfolio.holdings.copy(),
                'prices': {t: prices[t]['Close'] for t in self.config['assets']}
            })
        if investments_made == 0:
            print("Warning: No investments were made during the backtest. Consider lowering the drop threshold.")
        return self.portfolio

class PerformanceAnalyzer:
    def __init__(self, portfolio):
        self.portfolio = portfolio
        self.equity_curve = pd.Series(
            [x['value'] for x in portfolio.value_history],
            index=[x['date'] for x in portfolio.value_history]
        )
        self.invested_curve = self._calculate_invested_curve()
        self.ticker_value_curves = self._calculate_ticker_value_curves_from_history()

    def _calculate_invested_curve(self):
        invested = 0
        invested_curve = []
        for entry in self.portfolio.value_history:
            date = entry['date']
            # Sum all cash added (from trade log)
            invested = sum([t['price'] * t['shares'] for t in self.portfolio.trade_log if t['type'] == 'BUY' and pd.to_datetime(t['date']) <= date])
            invested_curve.append(invested)
        return pd.Series(invested_curve, index=[x['date'] for x in self.portfolio.value_history])

    def _calculate_ticker_value_curves_from_history(self):
        ticker_curves = {ticker: [] for ticker in self.portfolio.holdings}
        dates = []
        for entry in self.portfolio.holdings_history:
            dates.append(entry['date'])
            for ticker in ticker_curves:
                shares = entry['holdings'].get(ticker, 0)
                price = entry['prices'].get(ticker, 0)
                ticker_curves[ticker].append(shares * price)
        for ticker in ticker_curves:
            ticker_curves[ticker] = pd.Series(ticker_curves[ticker], index=dates)
        return ticker_curves

    def calculate_metrics(self):
        # Filter out zero or NaN values in the equity curve
        equity = self.equity_curve.replace([np.inf, -np.inf], np.nan).dropna()
        equity = equity[equity > 0]
        if len(equity) < 2:
            print("Warning: Not enough valid equity data to calculate metrics.")
            return {
                'annual_return': np.nan,
                'annual_volatility': np.nan,
                'sharpe_ratio': np.nan,
                'max_drawdown': np.nan
            }
        returns = equity.pct_change().dropna()
        annual_return = (1 + returns.mean()) ** 252 - 1
        annual_volatility = returns.std() * (252 ** 0.5)
        sharpe_ratio = annual_return / annual_volatility if annual_volatility != 0 else np.nan
        peak = equity.expanding(min_periods=1).max()
        drawdown = (equity - peak) / peak
        max_drawdown = drawdown.min()
        return {
            'annual_return': annual_return,
            'annual_volatility': annual_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown
        }

    def generate_plots(self):
        # Equity curve
        plt.figure(figsize=(12, 6))
        self.equity_curve.plot(label='Portfolio Equity')
        self.invested_curve.plot(label='Cumulative Invested Money', linestyle='--')
        plt.title('Portfolio Equity Curve vs. Invested Money')
        plt.legend()
        plt.savefig('equity_curve.png')
        plt.close()
        # Drawdown
        plt.figure(figsize=(12, 6))
        (self.equity_curve / self.equity_curve.cummax() - 1).plot(label='Drawdown')
        plt.title('Portfolio Drawdown')
        plt.savefig('drawdown.png')
        plt.close()

    def save_trade_log(self):
        pd.DataFrame(self.portfolio.trade_log).to_csv('trade_log.csv', index=False)

    def plot_per_ticker_drawdown(self):
        plt.figure(figsize=(12, 6))
        for ticker, curve in self.ticker_value_curves.items():
            peak = curve.expanding(min_periods=1).max()
            drawdown = (curve - peak) / peak
            drawdown.plot(label=f'{ticker}')
        plt.title('Per-Ticker Drawdown Curves')
        plt.ylabel('Drawdown')
        plt.xlabel('Date')
        plt.legend()
        plt.savefig('per_ticker_drawdown.png')
        plt.close()

    def plot_equity_forecast(self, years=5, investment_amount=100, confidence=0.95):
        last_backtest_date = self.equity_curve.index[-1]
        start_value = self.equity_curve.iloc[-1]
        n_weeks = years * 52
        dt = 1/52
        # Build backtest cumulative invested array
        backtest_dates = self.invested_curve.index
        backtest_invested = self.invested_curve.values
        total_invested_so_far = backtest_invested[-1] if len(backtest_invested) > 0 else 0
        metrics = self.calculate_metrics()
        mu = metrics['annual_return']
        sigma = metrics['annual_volatility']
        forecast = np.zeros(n_weeks)
        forecast[0] = start_value + investment_amount
        invested = np.zeros(n_weeks)
        invested[0] = total_invested_so_far
        intervals = {
            '50%': norm.ppf(0.5 + 0.50/2),
            '80%': norm.ppf(0.5 + 0.80/2),
            '95%': norm.ppf(0.5 + 0.95/2),
            '98%': norm.ppf(0.5 + 0.98/2),
            '99%': norm.ppf(0.5 + 0.99/2),
            '99.9%': norm.ppf(0.5 + 0.999/2),
        }
        bounds = {k: {'upper': np.zeros(n_weeks), 'lower': np.zeros(n_weeks)} for k in intervals}
        for k in bounds:
            bounds[k]['upper'][0] = forecast[0]
            bounds[k]['lower'][0] = forecast[0]
        for t in range(1, n_weeks):
            forecast[t] = forecast[t-1] * np.exp((mu - 0.5 * sigma**2) * dt) + investment_amount
            invested[t] = invested[t-1] + investment_amount
            for k, z in intervals.items():
                bounds[k]['upper'][t] = forecast[t] * np.exp(z * sigma * np.sqrt(dt))
                bounds[k]['lower'][t] = forecast[t] * np.exp(-z * sigma * np.sqrt(dt))
        forecast_dates = pd.date_range(start=last_backtest_date + pd.Timedelta(weeks=1), periods=n_weeks, freq='W')
        all_invested = np.concatenate([backtest_invested, invested[1:]])
        all_dates = backtest_dates.tolist() + forecast_dates[1:].tolist()
        plt.figure(figsize=(12, 6))
        plt.plot(self.equity_curve.index, self.equity_curve.values, label='Backtest Equity Curve', color='tab:blue', linewidth=2, alpha=0.7)
        plt.plot(forecast_dates, forecast, label='Median Forecast')
        plt.plot(all_dates, all_invested, label='Cumulative Invested Money', linestyle='--')
        colors = {
            '50%': 'blue',
            '80%': 'green',
            '95%': 'orange',
            '98%': 'red',
            '99%': 'purple',
            '99.9%': 'black',
        }
        alphas = {
            '50%': 0.15,
            '80%': 0.15,
            '95%': 0.15,
            '98%': 0.10,
            '99%': 0.10,
            '99.9%': 0.07,
        }
        for k in intervals:
            plt.fill_between(forecast_dates, bounds[k]['lower'], bounds[k]['upper'], color=colors[k], alpha=alphas[k], label=f'{k} Confidence Interval')
        plt.title(f'Projected Equity Curve (Next {years} Years, {investment_amount} USD/investment)')
        plt.xlabel('Date')
        plt.ylabel('Portfolio Value')
        plt.legend()
        for year in range(1, years + 1):
            idx = year * 52 - 1
            if idx < len(forecast):
                year_forecast = forecast[idx]
                year_invested = invested[idx]
                plt.annotate(f'ROI: {((year_forecast - year_invested) / year_invested * 100) if year_invested > 0 else 0:.2f}%',
                             xy=(forecast_dates[idx], year_forecast),
                             xytext=(-60, 20), textcoords='offset points',
                             arrowprops=dict(arrowstyle='->', color='black'),
                             fontsize=10, color='black',
                             bbox=dict(boxstyle='round,pad=0.2', fc='yellow', alpha=0.4))
        final_forecast = forecast[-1]
        final_invested = invested[-1]
        roi = (final_forecast - final_invested) / final_invested if final_invested > 0 else 0
        roi_pct = roi * 100
        plt.annotate(f'Total ROI: {roi_pct:.2f}%',
                     xy=(forecast_dates[-1], final_forecast),
                     xytext=(-180, 30), textcoords='offset points',
                     arrowprops=dict(arrowstyle='->', color='black'),
                     fontsize=12, color='black',
                     bbox=dict(boxstyle='round,pad=0.3', fc='yellow', alpha=0.5))
        plt.savefig('equity_forecast.png')
        plt.close()

# Configuration section
CONFIG = {
    'start_date': '2004-11-18',
    'end_date': '2025-05-05',
    'initial_capital': 0,
    'assets': {
        'GLD': 0.35,
        'SPY': 0.30,
        'DJI': 0.10,
        'QQQ': 0.15,
        'BRK-A': 0.10,
    },
    'drop_threshold': 0.01,  # 5% drop
    'investment_amount': 100
}

if __name__ == "__main__":
    engine = BacktestEngine(CONFIG)
    portfolio = engine.run_simulation()
    analyzer = PerformanceAnalyzer(portfolio)
    metrics = analyzer.calculate_metrics()
    analyzer.generate_plots()
    analyzer.save_trade_log()
    analyzer.plot_per_ticker_drawdown()
    analyzer.plot_equity_forecast()
    print("Backtest completed successfully!")
    print("Performance Metrics:")
    for k, v in metrics.items():
        print(f"{k}: {v:.4f}") 