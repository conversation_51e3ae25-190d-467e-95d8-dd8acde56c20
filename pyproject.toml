[project]
name = "investment-backtester"
version = "1.0.0"
description = "A modular investment strategy backtesting system"
authors = [
    {name = "Investment Backtester", email = "<EMAIL>"}
]
dependencies = [
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "matplotlib>=3.7.0",
    "scipy>=1.10.0",
    "yfinance>=0.2.0",
    "pandas-market-calendars>=4.0.0",
    "PyYAML>=6.0",
    "streamlit>=1.28.0",
    "plotly>=5.15.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["engine", "strategies", "config", "utils", "ui"]

[tool.uv]
dev-dependencies = []
