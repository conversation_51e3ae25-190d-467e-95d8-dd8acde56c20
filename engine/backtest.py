import yfinance as yf
import pandas as pd
import matplotlib.pyplot as plt
from pandas_market_calendars import get_calendar
from datetime import datetime
import numpy as np
from scipy.stats import norm
import os
from utils.data_loader import load_data
from utils.data_downloader import download_and_save_data

class DataFetcher:
    def __init__(self):
        self.cal = get_calendar('NYSE')
    
    def fetch_ohlcv(self, tickers, start_date, end_date):
        """Fetch historical OHLCV data with calendar alignment, using cache if available"""
        schedule = self.cal.schedule(start_date, end_date)
        valid_dates = schedule.index
        all_data = {}
        for ticker in tickers:
            filename = f"data_{ticker}.csv"
            filepath = os.path.join("data_cache", filename)
            
            # Try to load existing data using our clean data loader
            data = load_data(filepath)
            
            if data is None or data.empty:
                # Download and save with clean format (simplified naming)
                data = download_and_save_data(ticker, start_date, end_date)
                if data is not None:
                    # Convert to the format expected by the rest of the code
                    data = data.set_index('Date')
            
            all_data[ticker] = data
        combined = pd.concat(all_data, axis=1)
        aligned_data = combined.reindex(valid_dates).ffill()
        return aligned_data

class Portfolio:
    def __init__(self, initial_capital, assets):
        self.cash = initial_capital
        self.holdings = {ticker: 0 for ticker in assets}
        self.allocations = assets
        self.value_history = []
        self.trade_log = []
        self.holdings_history = []  # List of dicts: {date, holdings: {ticker: shares}, prices: {ticker: price}}
    
    def execute_trade(self, date, ticker, price, trade_value=None, shares=None):
        """Execute fractional share trade - either specify trade_value or shares"""
        if trade_value is not None:
            shares = trade_value / price
        elif shares is None:
            raise ValueError("Must specify either trade_value or shares")
        
        if shares > 0 and trade_value <= self.cash:
            self.holdings[ticker] += shares
            self.cash -= trade_value if trade_value else shares * price
            self.trade_log.append({
                'date': date,
                'ticker': ticker,
                'shares': shares,
                'price': price,
                'type': 'BUY'
            })
            return True
        return False
    
    def reinvest_dividends(self, date, ticker, dividend, price):
        """Reinvest dividends at adjusted close price"""
        if dividend > 0 and self.holdings[ticker] > 0:
            dividend_amount = self.holdings[ticker] * dividend
            shares = dividend_amount / price
            self.holdings[ticker] += shares
            self.trade_log.append({
                'date': date,
                'ticker': ticker,
                'shares': shares,
                'price': price,
                'type': 'DIVIDEND'
            })
    
    def portfolio_value(self):
        if not hasattr(self, 'current_prices'):
            return self.cash
        holdings_value = sum(
            self.holdings[ticker] * self.current_prices[ticker]['Close']
            for ticker in self.holdings
        )
        return holdings_value + self.cash

class BacktestEngine:
    def __init__(self, config, strategy):
        self.config = config
        self.strategy = strategy
        self.data_fetcher = DataFetcher()
        self.portfolio = Portfolio(
            config['initial_capital'], 
            config['assets']
        )
    
    def run_simulation(self):
        # Fetch and prepare data
        data = self.data_fetcher.fetch_ohlcv(
            list(self.config['assets'].keys()),
            self.config['start_date'],
            self.config['end_date']
        )
        
        # Check for tickers missing data at the start date
        start_date = pd.to_datetime(self.config['start_date'])
        for ticker in self.config['assets']:
            try:
                first_valid = data[ticker]['Close'].first_valid_index()
                if first_valid is not None and pd.to_datetime(first_valid) > start_date:
                    print(f"Warning: {ticker} has no data at start date {start_date.date()}. First available: {first_valid.date()}")
                elif first_valid is None:
                    print(f"Warning: {ticker} has no valid data in the selected period.")
            except Exception as e:
                print(f"Warning: Could not check data for {ticker}: {e}")
        
        # Initialize strategy
        self.strategy.initialize(self.config, self.portfolio)
        
        # Main simulation loop
        for date, prices in data.iterrows():
            # Store all current prices for the day
            self.portfolio.current_prices = {
                t: {'Close': prices[t]['Close']} for t in self.config['assets']
            }
            
            # Dividend reinvestment
            for ticker in self.config['assets']:
                if 'Dividends' in prices[ticker]:
                    self.portfolio.reinvest_dividends(
                        date, ticker,
                        prices[ticker]['Dividends'],
                        prices[ticker]['Close']
                    )
            
            # Execute strategy logic
            self.strategy.execute(date, prices, self.portfolio)
            
            # Record daily portfolio value
            self.portfolio.value_history.append({
                'date': date,
                'value': self.portfolio.portfolio_value()
            })
            
            # Record holdings and prices for each ticker
            self.portfolio.holdings_history.append({
                'date': date,
                'holdings': self.portfolio.holdings.copy(),
                'prices': {t: prices[t]['Close'] for t in self.config['assets']}
            })
        
        return self.portfolio

class PerformanceAnalyzer:
    def __init__(self, portfolio):
        self.portfolio = portfolio
        self.equity_curve = pd.Series(
            [x['value'] for x in portfolio.value_history],
            index=[x['date'] for x in portfolio.value_history]
        )
        # Calculate cumulative invested money (total cash added)
        self.invested_curve = self._calculate_invested_curve()
        # Prepare per-ticker holding value curves from holdings_history
        self.ticker_value_curves = self._calculate_ticker_value_curves_from_history()
    
    def _calculate_invested_curve(self):
        # Calculate invested money from trade log
        invested_curve = []
        for entry in self.portfolio.value_history:
            date = entry['date']
            # Sum all cash added (from trade log) up to this date
            invested = sum([
                t['price'] * t['shares'] for t in self.portfolio.trade_log 
                if t['type'] == 'BUY' and pd.to_datetime(t['date']) <= date
            ])
            invested_curve.append(invested)
        return pd.Series(invested_curve, index=[x['date'] for x in self.portfolio.value_history])
    
    def _calculate_ticker_value_curves_from_history(self):
        ticker_curves = {ticker: [] for ticker in self.portfolio.holdings}
        dates = []
        for entry in self.portfolio.holdings_history:
            dates.append(entry['date'])
            for ticker in ticker_curves:
                shares = entry['holdings'].get(ticker, 0)
                price = entry['prices'].get(ticker, 0)
                ticker_curves[ticker].append(shares * price)
        for ticker in ticker_curves:
            ticker_curves[ticker] = pd.Series(ticker_curves[ticker], index=dates)
        return ticker_curves
    
    def calculate_metrics(self):
        # Calculate returns
        returns = self.equity_curve.pct_change(fill_method=None).dropna()
        
        if len(returns) < 2:
            return {
                'annual_return': 0,
                'annual_volatility': 0,
                'sharpe_ratio': 0,
                'max_drawdown': 0
            }
        
        # Annualized return
        annual_return = (1 + returns.mean()) ** 252 - 1
        
        # Annualized volatility
        annual_volatility = returns.std() * (252 ** 0.5)
        
        # Sharpe ratio (risk-free rate=0)
        sharpe_ratio = annual_return / annual_volatility if annual_volatility != 0 else 0
        
        # Maximum drawdown
        peak = self.equity_curve.expanding(min_periods=1).max()
        drawdown = (self.equity_curve - peak) / peak
        max_drawdown = drawdown.min()
        
        return {
            'annual_return': annual_return,
            'annual_volatility': annual_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown
        }
    
    def generate_plots(self):
        # Equity curve
        plt.figure(figsize=(12, 6))
        self.equity_curve.plot(label='Portfolio Equity')
        self.invested_curve.plot(label='Cumulative Invested Money', linestyle='--')
        plt.title('Portfolio Equity Curve vs. Invested Money')
        plt.legend()
        plt.savefig('equity_curve.png')
        plt.close()
        
        # Drawdown
        plt.figure(figsize=(12, 6))
        (self.equity_curve / self.equity_curve.cummax() - 1).plot(
            title='Portfolio Drawdown'
        )
        plt.savefig('drawdown.png')
        plt.close()
    
    def save_trade_log(self):
        pd.DataFrame(self.portfolio.trade_log).to_csv('trade_log.csv', index=False)
    
    def plot_per_ticker_drawdown(self):
        plt.figure(figsize=(12, 6))
        for ticker, curve in self.ticker_value_curves.items():
            peak = curve.expanding(min_periods=1).max()
            drawdown = (curve - peak) / peak
            drawdown.plot(label=f'{ticker}')
        plt.title('Per-Ticker Drawdown Curves')
        plt.ylabel('Drawdown')
        plt.xlabel('Date')
        plt.legend()
        plt.savefig('per_ticker_drawdown.png')
        plt.close()

    def plot_equity_forecast(self, years=10, weekly_investment=100, confidence=0.95):
        """
        Generate realistic equity forecasts using multiple approaches:
        1. Conservative linear growth based on historical average
        2. Market-based assumptions (7% real return)
        3. Monte Carlo simulation with mean reversion
        """
        last_backtest_date = self.equity_curve.index[-1]
        start_value = self.equity_curve.iloc[-1]
        n_weeks = years * 52

        # Build backtest data
        backtest_dates = [x['date'] for x in self.portfolio.value_history]
        backtest_invested = self.invested_curve.values
        total_invested_so_far = backtest_invested[-1] if len(backtest_invested) > 0 else 0

        # Calculate realistic parameters
        metrics = self.calculate_metrics()
        historical_return = metrics['annual_return']
        historical_volatility = metrics['annual_volatility']

        # Use more conservative assumptions
        # Cap historical returns to reasonable bounds and use market assumptions
        conservative_return = min(max(historical_return, 0.04), 0.12)  # Between 4% and 12%
        market_return = 0.07  # Standard 7% real market return assumption
        blended_return = (conservative_return + market_return) / 2  # Blend historical and market

        # Reduce volatility for long-term forecasts (mean reversion effect)
        long_term_volatility = min(historical_volatility * 0.7, 0.20)  # Cap at 20%

        forecast_dates = pd.date_range(start=last_backtest_date + pd.Timedelta(weeks=1), periods=n_weeks, freq='W')

        # Method 1: Conservative Linear Growth
        linear_forecast = self._linear_growth_forecast(start_value, total_invested_so_far,
                                                     weekly_investment, n_weeks, blended_return)

        # Method 2: Monte Carlo with Mean Reversion
        mc_scenarios = self._monte_carlo_forecast(start_value, total_invested_so_far,
                                                weekly_investment, n_weeks, blended_return,
                                                long_term_volatility, num_scenarios=1000)

        # Calculate percentiles from Monte Carlo
        percentiles = {
            '10%': np.percentile(mc_scenarios, 10, axis=0),
            '25%': np.percentile(mc_scenarios, 25, axis=0),
            '50%': np.percentile(mc_scenarios, 50, axis=0),
            '75%': np.percentile(mc_scenarios, 75, axis=0),
            '90%': np.percentile(mc_scenarios, 90, axis=0),
        }

        # Calculate invested money over time
        invested_forecast = np.array([total_invested_so_far + (i + 1) * weekly_investment for i in range(n_weeks)])

        # Combine backtest and forecast data
        all_invested = np.concatenate([backtest_invested, invested_forecast])
        all_dates = backtest_dates + forecast_dates.tolist()

        # Create the plot
        plt.figure(figsize=(14, 8))

        # Plot backtest equity curve
        plt.plot(self.equity_curve.index, self.equity_curve.values,
                label='Historical Performance', color='tab:blue', linewidth=2, alpha=0.8)

        # Plot forecasts
        plt.plot(forecast_dates, linear_forecast,
                label=f'Conservative Linear ({blended_return:.1%} annual)',
                color='green', linewidth=2, linestyle='--')

        plt.plot(forecast_dates, percentiles['50%'],
                label='Monte Carlo Median', color='orange', linewidth=2)

        # Plot confidence bands
        plt.fill_between(forecast_dates, percentiles['25%'], percentiles['75%'],
                        color='orange', alpha=0.3, label='25th-75th Percentile')
        plt.fill_between(forecast_dates, percentiles['10%'], percentiles['90%'],
                        color='orange', alpha=0.15, label='10th-90th Percentile')

        # Plot invested money
        plt.plot(all_dates, all_invested,
                label='Cumulative Invested Money', linestyle=':', color='gray', linewidth=2)

        plt.title(f'Realistic Portfolio Forecast (Next {years} Years, ${weekly_investment}/week)\n'
                 f'Based on {blended_return:.1%} annual return, {long_term_volatility:.1%} volatility')
        plt.xlabel('Date')
        plt.ylabel('Portfolio Value ($)')
        plt.legend(loc='upper left')
        plt.grid(True, alpha=0.3)

        # Add annotations for key milestones
        self._add_forecast_annotations(forecast_dates, linear_forecast, invested_forecast, years)

        plt.tight_layout()
        plt.savefig('equity_forecast.png', dpi=300, bbox_inches='tight')
        plt.close()

        # Print forecast summary
        self._print_forecast_summary(linear_forecast, percentiles, invested_forecast, years, weekly_investment)

    def _linear_growth_forecast(self, start_value, start_invested, weekly_investment, n_weeks, annual_return):
        """Conservative linear growth model"""
        weekly_return = annual_return / 52
        forecast = np.zeros(n_weeks)

        for i in range(n_weeks):
            # Add weekly investment
            new_investment = weekly_investment
            # Apply return to existing portfolio value
            if i == 0:
                portfolio_growth = start_value * weekly_return
                forecast[i] = start_value + new_investment + portfolio_growth
            else:
                portfolio_growth = forecast[i-1] * weekly_return
                forecast[i] = forecast[i-1] + new_investment + portfolio_growth

        return forecast

    def _monte_carlo_forecast(self, start_value, start_invested, weekly_investment, n_weeks,
                            annual_return, annual_volatility, num_scenarios=1000):
        """Monte Carlo simulation with mean reversion"""
        weekly_return = annual_return / 52
        weekly_volatility = annual_volatility / np.sqrt(52)

        # Mean reversion parameters
        mean_reversion_speed = 0.1  # How quickly returns revert to mean

        scenarios = np.zeros((num_scenarios, n_weeks))

        for scenario in range(num_scenarios):
            portfolio_value = start_value
            returns = []

            for week in range(n_weeks):
                # Generate return with mean reversion
                if len(returns) > 0:
                    # Recent average return
                    recent_avg = np.mean(returns[-min(52, len(returns)):])  # Last year or available
                    # Mean reversion adjustment
                    reversion_adjustment = mean_reversion_speed * (weekly_return - recent_avg)
                    adjusted_return = weekly_return + reversion_adjustment
                else:
                    adjusted_return = weekly_return

                # Add random shock
                random_shock = np.random.normal(0, weekly_volatility)
                weekly_ret = adjusted_return + random_shock
                returns.append(weekly_ret)

                # Apply to portfolio
                portfolio_growth = portfolio_value * weekly_ret
                portfolio_value = portfolio_value + weekly_investment + portfolio_growth
                scenarios[scenario, week] = portfolio_value

        return scenarios

    def _add_forecast_annotations(self, forecast_dates, linear_forecast, invested_forecast, years):
        """Add milestone annotations to the forecast chart"""
        for year in [1, 5, years]:
            if year <= years:
                idx = min(year * 52 - 1, len(linear_forecast) - 1)
                if idx >= 0:
                    forecast_value = linear_forecast[idx]
                    invested_value = invested_forecast[idx]
                    roi = ((forecast_value - invested_value) / invested_value * 100) if invested_value > 0 else 0

                    plt.annotate(f'Year {year}\nROI: {roi:.1f}%\n${forecast_value:,.0f}',
                               xy=(forecast_dates[idx], forecast_value),
                               xytext=(10, 20), textcoords='offset points',
                               bbox=dict(boxstyle='round,pad=0.3', fc='lightblue', alpha=0.7),
                               arrowprops=dict(arrowstyle='->', color='blue'),
                               fontsize=9)

    def _print_forecast_summary(self, linear_forecast, percentiles, invested_forecast, years, weekly_investment):
        """Print a summary of the forecast results"""
        final_linear = linear_forecast[-1]
        final_invested = invested_forecast[-1]
        final_median = percentiles['50%'][-1]
        final_p25 = percentiles['25%'][-1]
        final_p75 = percentiles['75%'][-1]

        print(f"\n📊 {years}-Year Forecast Summary (${weekly_investment}/week):")
        print(f"   💰 Total Investment: ${final_invested:,.0f}")
        print(f"   📈 Conservative Linear: ${final_linear:,.0f} (ROI: {((final_linear-final_invested)/final_invested*100):.1f}%)")
        print(f"   🎯 Monte Carlo Median: ${final_median:,.0f} (ROI: {((final_median-final_invested)/final_invested*100):.1f}%)")
        print(f"   📊 Monte Carlo Range: ${final_p25:,.0f} - ${final_p75:,.0f}")
        print(f"   ⚠️  Note: Forecasts are estimates based on historical data and market assumptions")
