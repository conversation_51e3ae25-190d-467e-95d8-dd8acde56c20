from utils.data_downloader import download_and_save_data
import os

# Test the updated data downloader
test_filepath = "test_data/test_AAPL.csv"

# Download a small sample
data = download_and_save_data("AAPL", "2024-01-01", "2024-01-10", test_filepath)

if data is not None:
    print("✅ Data downloaded successfully!")
    print(f"Shape: {data.shape}")
    print(f"Columns: {list(data.columns)}")
    print("\nFirst 5 rows:")
    print(data.head())
    
    # Check the actual CSV file content
    print("\n" + "="*50)
    print("CSV file content (first 10 lines):")
    print("="*50)
    
    if os.path.exists(test_filepath):
        with open(test_filepath, 'r') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[:10]):
                print(f"{i+1:2d}: {line.rstrip()}")
    
    # Clean up test file
    if os.path.exists(test_filepath):
        os.remove(test_filepath)
    if os.path.exists("test_data"):
        os.rmdir("test_data")
        
else:
    print("❌ Failed to download data")
