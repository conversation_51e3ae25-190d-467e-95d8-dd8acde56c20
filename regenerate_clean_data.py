import os
import glob

# Remove all existing CSV files so they get regenerated with clean format
data_cache_dir = "data_cache"

if os.path.exists(data_cache_dir):
    csv_files = glob.glob(os.path.join(data_cache_dir, "*.csv"))
    
    print(f"Found {len(csv_files)} CSV files to remove:")
    for csv_file in csv_files:
        print(f"  - {os.path.basename(csv_file)}")
        os.remove(csv_file)
    
    print(f"\n✅ Removed {len(csv_files)} CSV files.")
    print("Next time you run backtester.py or backtest2.py, they will be regenerated with clean headers!")
else:
    print("No data_cache directory found.")
