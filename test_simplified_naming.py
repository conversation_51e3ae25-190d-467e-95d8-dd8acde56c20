from utils.data_downloader import download_and_save_data
from backtester import DataFetcher
import os
import glob

print("Testing simplified naming convention...")

# Test 1: Direct downloader usage
print("\n1. Testing data_downloader with simplified naming:")
data = download_and_save_data("AAPL", "2024-01-01", "2024-01-10")

if data is not None:
    expected_path = "data_cache/data_AAPL.csv"
    if os.path.exists(expected_path):
        print(f"✅ File created with simplified name: {expected_path}")
        
        # Check header
        with open(expected_path, 'r') as f:
            header = f.readline().strip()
            print(f"   Header: {header}")
            if header == "Date,Close,High,Low,Open,Volume":
                print("   ✅ Clean header format!")
            else:
                print("   ❌ Unexpected header format!")
        
        # Clean up
        os.remove(expected_path)
    else:
        print(f"❌ Expected file not found: {expected_path}")

# Test 2: DataFetcher usage
print("\n2. Testing DataFetcher with simplified naming:")
data_fetcher = DataFetcher()
data = data_fetcher.fetch_ohlcv(['SPY'], '2024-01-01', '2024-01-10')

if data is not None and not data.empty:
    expected_path = "data_cache/data_SPY.csv"
    if os.path.exists(expected_path):
        print(f"✅ File created with simplified name: {expected_path}")
        
        # Check header
        with open(expected_path, 'r') as f:
            header = f.readline().strip()
            print(f"   Header: {header}")
        
        # Clean up
        os.remove(expected_path)
    else:
        print(f"❌ Expected file not found: {expected_path}")

# Clean up any remaining test files
test_files = glob.glob("data_cache/data_*.csv")
for file in test_files:
    if "AAPL" in file or "SPY" in file:
        os.remove(file)
        print(f"Cleaned up: {file}")

print("\n🎉 Simplified naming convention is working!")
print("Now CSV files will be named: data_TICKER.csv")
