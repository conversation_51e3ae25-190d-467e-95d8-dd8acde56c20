#!/usr/bin/env python3
"""
Quick test of the modular strategy system
"""

from engine.backtest import BacktestEngine, PerformanceAnalyzer
from strategies.weekly_invest import WeeklyInvestStrategy
from strategies.drawdown_invest import DrawdownInvestStrategy

# Test configuration with a short period
TEST_CONFIG = {
    'start_date': '2023-12-01',
    'end_date': '2023-12-31',
    'initial_capital': 0,
    'assets': {
        'SPY': 0.50,
        'QQQ': 0.50,
    }
}

def test_strategy(strategy_name, strategy_instance):
    """Test a strategy and return basic metrics"""
    print(f"\n🧪 Testing {strategy_name}...")
    print(f"📝 {strategy_instance.get_description()}")
    
    engine = BacktestEngine(TEST_CONFIG, strategy_instance)
    portfolio = engine.run_simulation()
    
    analyzer = PerformanceAnalyzer(portfolio)
    metrics = analyzer.calculate_metrics()
    
    total_trades = len(portfolio.trade_log)
    buy_trades = len([t for t in portfolio.trade_log if t['type'] == 'BUY'])
    final_value = portfolio.portfolio_value()
    total_invested = sum([t['price'] * t['shares'] for t in portfolio.trade_log if t['type'] == 'BUY'])
    
    print(f"   ✅ Trades: {buy_trades}")
    print(f"   💰 Invested: ${total_invested:.2f}")
    print(f"   📈 Final Value: ${final_value:.2f}")
    print(f"   📊 Annual Return: {metrics['annual_return']:.4f}")
    
    return {
        'trades': buy_trades,
        'invested': total_invested,
        'final_value': final_value,
        'annual_return': metrics['annual_return']
    }

def main():
    print("🚀 Testing Modular Strategy System")
    print("=" * 50)
    
    # Test WeeklyInvest
    weekly_strategy = WeeklyInvestStrategy(weekly_amount=100, investment_day='monday')
    weekly_results = test_strategy("WeeklyInvest", weekly_strategy)
    
    # Test DrawdownInvest
    drawdown_strategy = DrawdownInvestStrategy(drop_threshold=0.02, investment_amount=100)
    drawdown_results = test_strategy("DrawdownInvest", drawdown_strategy)
    
    print(f"\n🎉 All tests completed successfully!")
    print(f"✅ WeeklyInvest: {weekly_results['trades']} trades, ${weekly_results['invested']:.2f} invested")
    print(f"✅ DrawdownInvest: {drawdown_results['trades']} trades, ${drawdown_results['invested']:.2f} invested")
    print(f"\n📁 Modular architecture is working perfectly!")
    print(f"   - Engine: engine/backtest.py")
    print(f"   - Strategies: strategies/")
    print(f"   - Main script: main.py")

if __name__ == "__main__":
    main()
