from utils.data_loader import load_data

# Test the updated data loader
filepath = "data_cache/data_BRK-A_2002-11-18_2025-05-05.csv"
data = load_data(filepath)

if data is not None:
    print("✅ Data loaded successfully!")
    print(f"Shape: {data.shape}")
    print(f"Columns: {list(data.columns)}")
    print(f"Index name: {data.index.name}")
    print("\nFirst 5 rows:")
    print(data.head())
    print("\nData types:")
    print(data.dtypes)
else:
    print("❌ Failed to load data")
