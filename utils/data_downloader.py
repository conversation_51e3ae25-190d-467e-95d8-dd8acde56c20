import yfinance as yf
import pandas as pd
import os

def download_and_save_data(ticker, start_date, end_date, filepath):
    """
    Downloads financial data and saves it with a clean, standard CSV format.
    Creates a single-row header: Date,Close,High,Low,Open,Volume
    """
    try:
        # Download data from yfinance
        data = yf.download(ticker, start=start_date, end=end_date)

        # Ensure directory exists
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # Reset index to make Date a column
        data = data.reset_index()

        # Reorder columns to match desired format: Date,Close,High,Low,Open,Volume
        # Note: yfinance returns columns in different order, so we need to reorder
        if 'Adj Close' in data.columns:
            data = data.drop('Adj Close', axis=1)  # Remove Adj Close if present

        # Ensure we have the expected columns and reorder them
        expected_columns = ['Date', 'Close', 'High', 'Low', 'Open', 'Volume']
        available_columns = [col for col in expected_columns if col in data.columns]
        data = data[available_columns]

        # Save with clean format
        data.to_csv(filepath, index=False)

        print(f"Successfully downloaded and saved {ticker} data to {filepath}")
        return data

    except Exception as e:
        print(f"Error downloading data for {ticker}: {e}")
        return None
