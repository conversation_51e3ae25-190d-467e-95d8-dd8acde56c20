from backtester import DataFetcher
import os

# Test the integration with a small date range
data_fetcher = DataFetcher()

print("Testing data fetcher integration...")
print("This will download SPY data and check the CSV format...")

# Test with a small date range
data = data_fetcher.fetch_ohlcv(['SPY'], '2024-01-01', '2024-01-10')

if data is not None and not data.empty:
    print("✅ Data fetched successfully!")
    print(f"Shape: {data.shape}")
    print(f"Columns: {list(data.columns)}")
    print("\nFirst 5 rows:")
    print(data.head())
    
    # Check the CSV file that was created
    csv_path = "data_cache/data_SPY_2024-01-01_2024-01-10.csv"
    if os.path.exists(csv_path):
        print(f"\n{'='*50}")
        print(f"CSV file content (first 5 lines): {csv_path}")
        print('='*50)
        
        with open(csv_path, 'r') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[:5]):
                print(f"{i+1:2d}: {line.rstrip()}")
        
        # Clean up test file
        os.remove(csv_path)
        print(f"\n✅ CSV format is correct! Clean single-row header.")
    else:
        print("❌ CSV file was not created")
        
else:
    print("❌ Failed to fetch data")
