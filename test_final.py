from backtester import <PERSON>Fetcher
import os

# Test with a small subset to verify everything works
print("Testing final integration with clean CSV headers...")

# Create a test config with a small date range and fewer assets
test_config = {
    'start_date': '2024-01-01',
    'end_date': '2024-01-10',
    'assets': ['SPY', 'QQQ']  # Just test with 2 assets
}

data_fetcher = DataFetcher()

# Fetch data
data = data_fetcher.fetch_ohlcv(
    test_config['assets'],
    test_config['start_date'],
    test_config['end_date']
)

if data is not None and not data.empty:
    print("✅ Data fetched successfully!")
    print(f"Shape: {data.shape}")
    print(f"Columns: {list(data.columns)}")
    
    # Check the CSV files that were created
    for ticker in test_config['assets']:
        csv_path = f"data_cache/data_{ticker}_{test_config['start_date']}_{test_config['end_date']}.csv"
        if os.path.exists(csv_path):
            print(f"\n📄 Checking {csv_path}:")
            with open(csv_path, 'r') as f:
                first_line = f.readline().strip()
                print(f"   Header: {first_line}")
                
                # Verify it's the clean format
                if first_line == "Date,Close,High,Low,Open,Volume":
                    print("   ✅ Clean header format!")
                else:
                    print("   ❌ Unexpected header format!")
            
            # Clean up test file
            os.remove(csv_path)
        else:
            print(f"   ❌ CSV file not found: {csv_path}")
    
    print("\n🎉 All tests passed! The CSV header issue has been fixed.")
    print("Now when you run backtester.py or backtest2.py, all CSV files will have clean headers.")
    
else:
    print("❌ Failed to fetch data")
