import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, date
import sys
import os
import io
import base64

# Add parent directory to path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engine.backtest import BacktestEngine, PerformanceAnalyzer
from strategies.weekly_invest import WeeklyInvestStrategy
from strategies.drawdown_invest import DrawdownInvestStrategy
from config.config_manager import get_config_manager

# Page configuration
st.set_page_config(
    page_title="Investment Strategy Backtester",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

def main():
    st.title("📈 Investment Strategy Backtester")
    st.markdown("---")
    
    # Sidebar for configuration
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # Strategy selection
        strategy_name = st.selectbox(
            "Select Strategy",
            ["WeeklyInvest", "DrawdownInvest"],
            help="Choose the investment strategy to backtest"
        )
        
        st.subheader("📅 Backtest Period")
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input(
                "Start Date",
                value=date(2020, 1, 1),
                min_value=date(2000, 1, 1),
                max_value=date.today()
            )
        with col2:
            end_date = st.date_input(
                "End Date",
                value=date(2024, 12, 31),
                min_value=start_date,
                max_value=date.today()
            )
        
        st.subheader("💼 Portfolio Assets")
        st.markdown("*Allocations must sum to 1.0*")
        
        # Asset allocation inputs
        assets = {}
        default_assets = {
            'GLD': 0.35,
            'SPY': 0.30,
            'VTI': 0.10,
            'QQQ': 0.15,
            'BRK-B': 0.10
        }
        
        for ticker, default_alloc in default_assets.items():
            assets[ticker] = st.number_input(
                f"{ticker}",
                min_value=0.0,
                max_value=1.0,
                value=default_alloc,
                step=0.05,
                format="%.2f"
            )
        
        # Check allocation sum
        total_allocation = sum(assets.values())
        if abs(total_allocation - 1.0) > 0.01:
            st.error(f"⚠️ Allocations sum to {total_allocation:.3f}, must equal 1.0")
        else:
            st.success(f"✅ Allocations sum to {total_allocation:.3f}")
        
        st.subheader("🎯 Strategy Parameters")
        
        # Strategy-specific parameters
        strategy_params = {}
        if strategy_name == "WeeklyInvest":
            strategy_params['weekly_amount'] = st.number_input(
                "Weekly Investment ($)",
                min_value=1,
                max_value=10000,
                value=100,
                step=10
            )
            strategy_params['investment_day'] = st.selectbox(
                "Investment Day",
                ["monday", "tuesday", "wednesday", "thursday", "friday"],
                index=0
            )
        
        elif strategy_name == "DrawdownInvest":
            strategy_params['drop_threshold'] = st.number_input(
                "Drop Threshold (%)",
                min_value=1.0,
                max_value=50.0,
                value=5.0,
                step=0.5,
                format="%.1f"
            ) / 100  # Convert to decimal
            
            strategy_params['investment_amount'] = st.number_input(
                "Investment Amount ($)",
                min_value=1,
                max_value=10000,
                value=100,
                step=10
            )
        
        # Run backtest button
        run_backtest = st.button(
            "🚀 Run Backtest",
            type="primary",
            disabled=abs(total_allocation - 1.0) > 0.01
        )
    
    # Main content area
    if run_backtest:
        run_backtest_analysis(
            strategy_name, 
            start_date, 
            end_date, 
            assets, 
            strategy_params
        )
    else:
        show_welcome_screen()

def show_welcome_screen():
    """Show welcome screen with instructions"""
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("""
        ## Welcome to the Investment Strategy Backtester! 🎯
        
        ### How to use:
        1. **Select a strategy** from the sidebar
        2. **Configure the backtest period** and portfolio assets
        3. **Adjust strategy parameters** to your preferences
        4. **Click "Run Backtest"** to see the results
        
        ### Available Strategies:
        
        **📊 WeeklyInvest**
        - Invests a fixed amount every week
        - Dollar-cost averaging approach
        - Configurable investment day and amount
        
        **📉 DrawdownInvest**
        - Invests when assets drop by a threshold
        - Buy-the-dip strategy
        - Configurable drop threshold and investment amount
        
        ### Features:
        - 📈 Interactive charts and performance metrics
        - 📊 Detailed trade logs and analysis
        - 🔮 Future projections with confidence intervals
        - ⚡ Fast, vectorized calculations
        - 📋 Data quality validation
        """)

def run_backtest_analysis(strategy_name, start_date, end_date, assets, strategy_params):
    """Run the backtest and display results"""
    
    # Create configuration
    config = {
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'initial_capital': 0,
        'assets': assets
    }
    
    # Create strategy
    if strategy_name == "WeeklyInvest":
        strategy = WeeklyInvestStrategy(**strategy_params)
    elif strategy_name == "DrawdownInvest":
        strategy = DrawdownInvestStrategy(**strategy_params)
    
    # Progress indicator
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    try:
        # Run backtest
        status_text.text("🔄 Initializing backtest engine...")
        progress_bar.progress(10)
        
        engine = BacktestEngine(config, strategy)
        
        status_text.text("📊 Fetching and validating data...")
        progress_bar.progress(30)
        
        portfolio = engine.run_simulation()
        
        status_text.text("📈 Analyzing performance...")
        progress_bar.progress(70)
        
        analyzer = PerformanceAnalyzer(portfolio)
        metrics = analyzer.calculate_metrics()
        
        status_text.text("🎨 Generating visualizations...")
        progress_bar.progress(90)
        
        # Display results
        display_results(portfolio, analyzer, metrics, strategy)
        
        progress_bar.progress(100)
        status_text.text("✅ Backtest completed successfully!")
        
    except Exception as e:
        st.error(f"❌ Error running backtest: {str(e)}")
        st.exception(e)

def display_results(portfolio, analyzer, metrics, strategy):
    """Display backtest results"""
    
    # Performance summary
    st.header("📊 Performance Summary")
    
    col1, col2, col3, col4 = st.columns(4)
    
    final_value = portfolio.portfolio_value()
    total_invested = sum([t['price'] * t['shares'] for t in portfolio.trade_log if t['type'] == 'BUY'])
    roi = ((final_value - total_invested) / total_invested * 100) if total_invested > 0 else 0
    
    with col1:
        st.metric("Final Portfolio Value", f"${final_value:,.2f}")
    with col2:
        st.metric("Total Invested", f"${total_invested:,.2f}")
    with col3:
        st.metric("Total Return", f"${final_value - total_invested:,.2f}")
    with col4:
        st.metric("ROI", f"{roi:.2f}%")
    
    # Performance metrics
    st.subheader("📈 Performance Metrics")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Annual Return", f"{metrics['annual_return']:.2%}")
    with col2:
        st.metric("Annual Volatility", f"{metrics['annual_volatility']:.2%}")
    with col3:
        st.metric("Sharpe Ratio", f"{metrics['sharpe_ratio']:.3f}")
    with col4:
        st.metric("Max Drawdown", f"{metrics['max_drawdown']:.2%}")
    
    # Equity curve chart
    st.subheader("📈 Equity Curve")
    
    equity_data = pd.DataFrame(portfolio.value_history)
    equity_data['date'] = pd.to_datetime(equity_data['date'])
    
    invested_data = pd.DataFrame({
        'date': equity_data['date'],
        'invested': analyzer.invested_curve.values
    })
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=equity_data['date'],
        y=equity_data['value'],
        mode='lines',
        name='Portfolio Value',
        line=dict(color='blue', width=2)
    ))
    
    fig.add_trace(go.Scatter(
        x=invested_data['date'],
        y=invested_data['invested'],
        mode='lines',
        name='Cumulative Invested',
        line=dict(color='gray', width=2, dash='dash')
    ))
    
    fig.update_layout(
        title="Portfolio Performance Over Time",
        xaxis_title="Date",
        yaxis_title="Value ($)",
        hovermode='x unified',
        height=500
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # Trade summary
    st.subheader("💼 Trade Summary")
    
    total_trades = len(portfolio.trade_log)
    buy_trades = len([t for t in portfolio.trade_log if t['type'] == 'BUY'])
    dividend_trades = len([t for t in portfolio.trade_log if t['type'] == 'DIVIDEND'])
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Total Trades", total_trades)
    with col2:
        st.metric("Buy Trades", buy_trades)
    with col3:
        st.metric("Dividend Reinvestments", dividend_trades)
    
    # Trade log
    if st.checkbox("Show Trade Log"):
        trade_df = pd.DataFrame(portfolio.trade_log)
        if not trade_df.empty:
            trade_df['date'] = pd.to_datetime(trade_df['date']).dt.date
            trade_df['value'] = trade_df['shares'] * trade_df['price']
            st.dataframe(
                trade_df[['date', 'ticker', 'type', 'shares', 'price', 'value']],
                use_container_width=True
            )

if __name__ == "__main__":
    main()
