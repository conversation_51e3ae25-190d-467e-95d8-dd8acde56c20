import pandas as pd
from .base_strategy import BaseStrategy

class DrawdownInvestStrategy(BaseStrategy):
    """Drawdown-based investment strategy - invests when assets drop by a certain percentage"""
    
    def __init__(self, drop_threshold=0.05, investment_amount=100):
        super().__init__()
        self.drop_threshold = drop_threshold
        self.investment_amount = investment_amount
        self.last_peaks = {}
        self.last_invest_dates = {}
        self.invested_sum = 0
    
    def get_name(self):
        return "DrawdownInvest"
    
    def get_description(self):
        return f"Invests ${self.investment_amount} when any asset drops {self.drop_threshold*100:.1f}% from its peak"
    
    def initialize(self, config, portfolio):
        """Initialize the strategy with config and portfolio"""
        super().initialize(config, portfolio)
        # Initialize tracking variables for each ticker
        self.last_peaks = {ticker: None for ticker in config['assets']}
        self.last_invest_dates = {ticker: None for ticker in config['assets']}
    
    def execute(self, date, prices, portfolio):
        """Execute drawdown-based investment logic"""
        investments_made = 0
        
        for ticker in self.config['assets']:
            price = prices[ticker]['Close']
            
            # Update peak price
            if self.last_peaks[ticker] is None or price > self.last_peaks[ticker]:
                self.last_peaks[ticker] = price
            
            # Calculate drop from peak
            drop = (self.last_peaks[ticker] - price) / self.last_peaks[ticker] if self.last_peaks[ticker] else 0
            
            # Check if we should invest
            if drop >= self.drop_threshold:
                # Invest only if not already invested on this drop
                if self.last_invest_dates[ticker] != date:
                    # Add investment money to cash
                    portfolio.cash += self.investment_amount
                    self.invested_sum += self.investment_amount
                    
                    # Execute trade
                    success = portfolio.execute_trade(
                        date, ticker, price, trade_value=self.investment_amount
                    )
                    
                    if success:
                        self.last_invest_dates[ticker] = date
                        investments_made += 1
                        print(f"Invested ${self.investment_amount} in {ticker} on {date.date()} at price {price:.2f} (drop {drop*100:.2f}%)")
        
        return investments_made
