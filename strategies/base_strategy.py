from abc import ABC, abstractmethod

class BaseStrategy(ABC):
    """Base class for all investment strategies"""
    
    def __init__(self):
        self.config = None
        self.portfolio = None
    
    def initialize(self, config, portfolio):
        """Initialize the strategy with config and portfolio"""
        self.config = config
        self.portfolio = portfolio
    
    @abstractmethod
    def execute(self, date, prices, portfolio):
        """Execute the strategy logic for a given date and prices"""
        pass
    
    @abstractmethod
    def get_name(self):
        """Return the name of the strategy"""
        pass
    
    @abstractmethod
    def get_description(self):
        """Return a description of the strategy"""
        pass
