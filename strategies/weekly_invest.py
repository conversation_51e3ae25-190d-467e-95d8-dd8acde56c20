from .base_strategy import BaseStrategy

class WeeklyInvestStrategy(BaseStrategy):
    """Weekly investment strategy - invests a fixed amount every week"""
    
    def __init__(self, weekly_amount=100, investment_day='monday'):
        super().__init__()
        self.weekly_amount = weekly_amount
        self.investment_day = investment_day
        self.week_counter = 0
    
    def get_name(self):
        return "WeeklyInvest"
    
    def get_description(self):
        return f"Invests ${self.weekly_amount} every {self.investment_day} according to portfolio allocations"
    
    def execute(self, date, prices, portfolio):
        """Execute weekly investment logic"""
        if self._is_investment_day(date):
            # Add weekly investment to cash
            portfolio.cash += self.weekly_amount
            
            # Invest according to allocations
            for ticker in self.config['assets']:
                allocation = self.config['assets'][ticker]
                target_value = portfolio.portfolio_value() * allocation
                current_value = portfolio.holdings[ticker] * prices[ticker]['Close']
                trade_value = target_value - current_value
                
                if trade_value > portfolio.cash:
                    trade_value = portfolio.cash
                
                if trade_value > 0:
                    portfolio.execute_trade(
                        date, ticker, prices[ticker]['Close'], trade_value=trade_value
                    )
    
    def _is_investment_day(self, date):
        """Check if current day is the configured investment day"""
        if self.investment_day.lower() == 'monday':
            return date.weekday() == 0  # Monday
        elif self.investment_day.lower() == 'tuesday':
            return date.weekday() == 1  # Tuesday
        elif self.investment_day.lower() == 'wednesday':
            return date.weekday() == 2  # Wednesday
        elif self.investment_day.lower() == 'thursday':
            return date.weekday() == 3  # Thursday
        elif self.investment_day.lower() == 'friday':
            return date.weekday() == 4  # Friday
        elif self.investment_day.lower() == 'first_trading_day':
            return date.weekday() == 0  # Monday (first trading day of week)
        return False
