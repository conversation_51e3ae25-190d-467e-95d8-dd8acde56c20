from backtester import DataFetcher
import os

print("Final test: Simplified naming with clean headers")
print("=" * 50)

# Test with multiple tickers
test_tickers = ['SPY', 'QQQ', 'GLD']
data_fetcher = DataFetcher()

# Fetch data (this will create the simplified CSV files)
data = data_fetcher.fetch_ohlcv(test_tickers, '2024-01-01', '2024-01-10')

if data is not None and not data.empty:
    print("✅ Data fetched successfully!")
    print(f"Shape: {data.shape}")
    
    # Check each CSV file
    for ticker in test_tickers:
        expected_path = f"data_cache/data_{ticker}.csv"
        if os.path.exists(expected_path):
            print(f"\n📄 {expected_path}:")
            
            # Check header
            with open(expected_path, 'r') as f:
                header = f.readline().strip()
                print(f"   Header: {header}")
                
                if header == "Date,Close,High,Low,Open,Volume":
                    print("   ✅ Perfect! Clean single-row header")
                else:
                    print("   ❌ Unexpected header format")
            
            # Show file size
            size = os.path.getsize(expected_path)
            print(f"   Size: {size:,} bytes")
            
            # Clean up
            os.remove(expected_path)
            print("   🗑️ Cleaned up test file")
        else:
            print(f"❌ File not found: {expected_path}")
    
    print(f"\n🎉 SUCCESS! All tests passed!")
    print(f"✅ Simplified naming: data_TICKER.csv")
    print(f"✅ Clean headers: Date,Close,High,Low,Open,Volume")
    print(f"✅ Integration works perfectly")
    
else:
    print("❌ Failed to fetch data")
