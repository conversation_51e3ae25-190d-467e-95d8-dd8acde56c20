import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional
import copy

class ConfigManager:
    """Manages configuration loading and validation"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_dir = Path(__file__).parent
        self.default_config_path = self.config_dir / "default_config.yaml"
        self.user_config_path = config_path
        self._config = None
        self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from files"""
        # Load default config
        with open(self.default_config_path, 'r') as f:
            self._config = yaml.safe_load(f)
        
        # Override with user config if provided
        if self.user_config_path and os.path.exists(self.user_config_path):
            with open(self.user_config_path, 'r') as f:
                user_config = yaml.safe_load(f)
                self._config = self._merge_configs(self._config, user_config)
        
        return self._config
    
    def _merge_configs(self, default: Dict, user: Dict) -> Dict:
        """Recursively merge user config into default config"""
        result = copy.deepcopy(default)
        
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation (e.g., 'backtest.start_date')"""
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """Set configuration value using dot notation"""
        keys = key_path.split('.')
        config = self._config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def get_strategy_config(self, strategy_name: str) -> Dict[str, Any]:
        """Get configuration for a specific strategy"""
        return self.get(f'strategies.{strategy_name}', {})
    
    def get_backtest_config(self) -> Dict[str, Any]:
        """Get backtest configuration"""
        return {
            'start_date': self.get('backtest.start_date'),
            'end_date': self.get('backtest.end_date'),
            'initial_capital': self.get('backtest.initial_capital'),
            'assets': self.get('portfolio.assets')
        }
    
    def save_config(self, filepath: str):
        """Save current configuration to file"""
        with open(filepath, 'w') as f:
            yaml.dump(self._config, f, default_flow_style=False, indent=2)
    
    def create_user_config_template(self, filepath: str):
        """Create a user config template with common overrides"""
        template = {
            'backtest': {
                'start_date': '2020-01-01',
                'end_date': '2024-12-31'
            },
            'strategies': {
                'WeeklyInvest': {
                    'weekly_amount': 200
                },
                'DrawdownInvest': {
                    'drop_threshold': 0.03,
                    'investment_amount': 150
                }
            }
        }
        
        with open(filepath, 'w') as f:
            yaml.dump(template, f, default_flow_style=False, indent=2)
        
        print(f"Created user config template at: {filepath}")
    
    def validate_config(self) -> list:
        """Validate configuration and return list of issues"""
        issues = []
        
        # Validate required fields
        required_fields = [
            'backtest.start_date',
            'backtest.end_date',
            'portfolio.assets'
        ]
        
        for field in required_fields:
            if self.get(field) is None:
                issues.append(f"Missing required field: {field}")
        
        # Validate portfolio allocations
        assets = self.get('portfolio.assets', {})
        if assets:
            total_allocation = sum(assets.values())
            if abs(total_allocation - 1.0) > 0.01:  # Allow 1% tolerance
                issues.append(f"Portfolio allocations sum to {total_allocation:.3f}, should be 1.0")
        
        # Validate date format
        import datetime
        try:
            datetime.datetime.strptime(self.get('backtest.start_date'), '%Y-%m-%d')
            datetime.datetime.strptime(self.get('backtest.end_date'), '%Y-%m-%d')
        except ValueError as e:
            issues.append(f"Invalid date format: {e}")
        
        return issues
    
    @property
    def config(self) -> Dict[str, Any]:
        """Get the full configuration dictionary"""
        return self._config

# Global config instance
_config_manager = None

def get_config_manager(config_path: Optional[str] = None) -> ConfigManager:
    """Get or create global config manager instance"""
    global _config_manager
    if _config_manager is None or config_path is not None:
        _config_manager = ConfigManager(config_path)
    return _config_manager

def get_config(key_path: str = None, default=None):
    """Convenience function to get config values"""
    manager = get_config_manager()
    if key_path is None:
        return manager.config
    return manager.get(key_path, default)
