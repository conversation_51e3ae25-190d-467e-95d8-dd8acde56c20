# Default Backtesting Configuration

# Backtest Parameters
backtest:
  start_date: "2002-11-18"
  end_date: "2025-05-05"
  initial_capital: 0

# Portfolio Assets and Allocations
portfolio:
  assets:
    GLD: 0.35    # Gold ETF
    SPY: 0.30    # S&P 500 ETF
    VTI: 0.10    # Total Stock Market ETF
    QQQ: 0.15    # NASDAQ ETF
    BRK-B: 0.10  # Berkshire Hathaway

# Strategy Configurations
strategies:
  WeeklyInvest:
    weekly_amount: 100
    investment_day: "monday"  # monday, tuesday, wednesday, thursday, friday, first_trading_day
    
  DrawdownInvest:
    drop_threshold: 0.05      # 5% drop threshold
    investment_amount: 100
    cooldown_days: 1          # Days to wait before investing in same asset again

# Data Settings
data:
  cache_directory: "data_cache"
  validate_data: true
  max_missing_data_pct: 10    # Maximum percentage of missing data allowed
  extreme_move_threshold: 0.5  # Flag price moves greater than 50% in one day

# Performance Settings
performance:
  use_vectorized_calculations: true
  batch_size: 1000            # For large datasets
  parallel_processing: false   # Enable for multiple strategies

# Forecast Settings
forecast:
  years: 10
  weekly_investment: 100
  confidence_levels: [0.10, 0.25, 0.50, 0.75, 0.90]
  monte_carlo_scenarios: 1000
  mean_reversion_speed: 0.1
  max_annual_return: 0.12     # Cap returns at 12%
  min_annual_return: 0.04     # Floor returns at 4%
  market_return_assumption: 0.07  # 7% market return
  volatility_reduction_factor: 0.7  # Reduce volatility for long-term forecasts

# Output Settings
output:
  generate_plots: true
  save_trade_log: true
  plot_dpi: 300
  figure_size: [14, 8]
  
# Logging
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  show_progress: true
  show_data_warnings: true
