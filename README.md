# Investment Strategy Backtesting System

A modular backtesting system for testing different investment strategies with clean data management and extensible architecture.

## 🏗️ Architecture

```
├── engine/
│   └── backtest.py          # Core backtesting engine
├── strategies/
│   ├── base_strategy.py     # Base strategy class
│   ├── weekly_invest.py     # Weekly investment strategy
│   └── drawdown_invest.py   # Drawdown-based investment strategy
├── utils/
│   ├── data_downloader.py   # Clean CSV data downloader
│   └── data_loader.py       # CSV data loader
├── main.py                  # Main script to run strategies
└── data_cache/              # Cached CSV files (data_TICKER.csv)
```

## 🚀 Usage

### Run Strategies

```bash
# Weekly investment strategy
uv run main.py WeeklyInvest

# Drawdown investment strategy  
uv run main.py DrawdownInvest

# With custom parameters
uv run main.py WeeklyInvest --weekly-amount 200 --investment-day friday
uv run main.py DrawdownInvest --drop-threshold 0.10 --investment-amount 150

# Custom date range
uv run main.py WeeklyInvest --start-date 2020-01-01 --end-date 2023-12-31
```

### Get Help

```bash
uv run main.py --help
```

## 📊 Available Strategies

### WeeklyInvest
- **Description**: Invests a fixed amount every week according to portfolio allocations
- **Parameters**:
  - `--weekly-amount`: Amount to invest each week (default: 100)
  - `--investment-day`: Day of week to invest (default: monday)

### DrawdownInvest  
- **Description**: Invests when any asset drops by a threshold percentage from its peak
- **Parameters**:
  - `--drop-threshold`: Drop percentage to trigger investment (default: 0.05)
  - `--investment-amount`: Amount to invest per trigger (default: 100)

## 📁 Output Files

Each backtest generates:
- `equity_curve.png` - Portfolio value vs invested money over time
- `drawdown.png` - Portfolio drawdown chart
- `per_ticker_drawdown.png` - Individual asset drawdown charts
- `equity_forecast.png` - Future projection with confidence intervals
- `trade_log.csv` - Detailed log of all trades

## 🔧 Data Management

### Clean CSV Format
All CSV files use a clean single-row header format:
```
Date,Close,High,Low,Open,Volume
2024-01-02,465.26,466.27,463.14,464.78,123623700
```

### Simplified Naming
CSV files are named simply: `data_TICKER.csv` (e.g., `data_SPY.csv`)

### Automatic Caching
- Data is automatically downloaded and cached
- Cached files are reused across different backtests
- No need to manually manage data files

## 🛠️ Adding New Strategies

1. Create a new file in `strategies/` directory
2. Inherit from `BaseStrategy` class
3. Implement required methods:
   - `get_name()`: Return strategy name
   - `get_description()`: Return strategy description  
   - `execute(date, prices, portfolio)`: Implement strategy logic

Example:
```python
from .base_strategy import BaseStrategy

class MyStrategy(BaseStrategy):
    def get_name(self):
        return "MyStrategy"
    
    def get_description(self):
        return "My custom investment strategy"
    
    def execute(self, date, prices, portfolio):
        # Implement your strategy logic here
        pass
```

4. Add to `main.py` strategy factory function

## 📈 Default Portfolio

The default portfolio allocation is:
- GLD (Gold): 35%
- SPY (S&P 500): 30% 
- VTI (Total Stock Market): 10%
- QQQ (NASDAQ): 15%
- BRK-B (Berkshire Hathaway): 10%

## 🎯 Features

- ✅ Modular architecture for easy strategy development
- ✅ Clean CSV data management with simplified naming
- ✅ Automatic data downloading and caching
- ✅ Comprehensive performance analysis
- ✅ Visual charts and forecasting
- ✅ Detailed trade logging
- ✅ Command-line interface with parameters
- ✅ Extensible strategy system

## 🔄 Migration from Old System

The old `backtester.py` and `backtest2.py` files have been refactored into this modular system:
- Core engine extracted to `engine/backtest.py`
- Strategy logic separated into `strategies/` modules
- Clean data management in `utils/`
- Unified interface through `main.py`
