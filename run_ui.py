#!/usr/bin/env python3
"""
Launch the Streamlit UI for the Investment Strategy Backtester

Usage:
    uv run run_ui.py
    python run_ui.py
"""

import subprocess
import sys
import os

def main():
    """Launch the Streamlit UI"""
    ui_path = os.path.join(os.path.dirname(__file__), "ui", "app.py")
    
    try:
        # Launch Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", ui_path,
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 UI server stopped")
    except Exception as e:
        print(f"❌ Error launching UI: {e}")
        print("Make sure Streamlit is installed: pip install streamlit")

if __name__ == "__main__":
    main()
